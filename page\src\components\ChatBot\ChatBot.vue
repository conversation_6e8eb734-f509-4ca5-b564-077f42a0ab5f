<template>
  <div class="chatbot-container">
    <!-- 常见问题区域 -->
    <div v-if="showCommonQuestions" class="common-questions">
      <h3 class="questions-title">常见问题</h3>
      <div class="questions-list">
        <div 
          v-for="(question, index) in commonQuestions" 
          :key="index"
          class="question-item"
          @click="selectQuestion(question)"
        >
          <a-icon type="question-circle" />
          {{ question }}
        </div>
      </div>
    </div>

    <!-- 聊天消息区域 -->
    <div v-if="!showCommonQuestions" class="chat-messages" ref="messagesContainer">
      <div 
        v-for="(message, index) in messages" 
        :key="index"
        :class="['message-item', message.type]"
      >
        <div class="message-avatar">
          <a-avatar v-if="message.type === 'user'" :size="32" icon="user" />
          <a-avatar v-else :size="32" :src="assistantAvatar" />
        </div>
        <div class="message-content">
          <div class="message-text">
            <div v-if="message.type === 'user'" v-text="message.content"></div>
            <div v-else v-html="renderMarkdown(message.content)"></div>
          </div>
          <div class="message-time">{{ formatTime(message.timestamp) }}</div>
        </div>
      </div>
      
      <!-- 正在输入指示器 -->
      <div v-if="isTyping" class="message-item assistant">
        <div class="message-avatar">
          <a-avatar :size="32" :src="assistantAvatar" />
        </div>
        <div class="message-content">
          <div class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-wrapper">
        <a-input
          v-model="inputText"
          placeholder="请输入您的问题..."
          :disabled="isTyping"
          @pressEnter="sendMessage"
          class="message-input"
        />
        <a-button 
          type="primary" 
          :loading="isTyping"
          @click="sendMessage"
          class="send-button"
        >
          发送
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { postAction } from '@/api/manage'

export default {
  name: 'ChatBot',
  data() {
    return {
      inputText: '',
      messages: [],
      isTyping: false,
      showCommonQuestions: true,
      assistantAvatar: 'https://img.alicdn.com/imgextra/i2/O1CN01Pda9nq1YDV0mnZ31H_!!6000000003025-54-tps-120-120.apng',
      commonQuestions: [
        '如何做出美观专业的Word方案？',
        '如何自定义word模版？',
        '快捷报价怎么用？',
        '素材库有什么用？'
      ]
    }
  },
  mounted() {
    // 组件挂载完成
  },
  methods: {
    
    selectQuestion(question) {
      this.inputText = question
      this.sendMessage()
    },
    
    async sendMessage() {
      if (!this.inputText.trim() || this.isTyping) return
      
      const userMessage = {
        type: 'user',
        content: this.inputText.trim(),
        timestamp: new Date()
      }
      
      this.messages.push(userMessage)
      this.showCommonQuestions = false
      this.inputText = ''
      this.isTyping = true
      
      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })
      
      try {
        // 调用AI接口
        const response = await this.callAIAPI(userMessage.content)
        
        // 创建助手消息
        const assistantMessage = {
          type: 'assistant',
          content: '',
          timestamp: new Date()
        }
        
        this.messages.push(assistantMessage)
        this.isTyping = false
        
        // 模拟流式输出
        await this.streamResponse(response, assistantMessage)
        
      } catch (error) {
        console.error('AI API调用失败:', error)
        this.isTyping = false
        
        const errorMessage = {
          type: 'assistant',
          content: '抱歉，我现在无法回答您的问题，请稍后再试。',
          timestamp: new Date()
        }
        
        this.messages.push(errorMessage)
      }
      
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },
    
    async callAIAPI(prompt) {
      try {
        // 使用现有的智能客服API端点
        const response = await fetch('https://webchat-bot-jm-mrhlyvmibi.cn-hangzhou.fcapp.run/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            prompt: prompt,
            conversationId: this.generateConversationId()
          })
        })

        if (!response.ok) {
          throw new Error('API请求失败')
        }

        return await response.text()
      } catch (error) {
        console.error('API调用失败:', error)
        // 返回一个默认回复
        return '抱歉，我现在无法回答您的问题。不过我可以为您提供一些常见问题的解答：\n\n**Word方案制作技巧：**\n- 使用专业模板可以快速制作美观的方案\n- 合理使用标题样式和格式化功能\n- 插入图表和图片增强视觉效果\n\n**快捷报价功能：**\n- 在系统中选择对应的服务项目\n- 系统会自动计算价格\n- 可以导出PDF格式的报价单\n\n如需更多帮助，请查看右侧的视频教程。'
      }
    },

    async streamResponse(content, message) {
      // 按句子分割，避免markdown语法被打断
      const sentences = this.splitBySentences(content)
      let currentContent = ''

      for (let i = 0; i < sentences.length; i++) {
        currentContent += sentences[i]
        message.content = currentContent

        // 更新显示
        this.$forceUpdate()

        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom()
        })

        // 控制输出速度，句子间隔稍长
        const delay = sentences[i].length > 50 ? 200 : 100
        await this.sleep(delay)
      }
    },

    splitBySentences(text) {
      // 按标点符号和换行符分割，保持markdown格式完整
      const parts = text.split(/([。！？\n\n])/g)
      const sentences = []
      let current = ''

      for (let part of parts) {
        current += part
        if (part.match(/[。！？\n\n]/)) {
          sentences.push(current)
          current = ''
        }
      }

      if (current) {
        sentences.push(current)
      }

      return sentences.filter(s => s.trim())
    },
    
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },
    
    renderMarkdown(content) {
      try {
        // 确保内容完整性，避免渲染不完整的markdown
        if (!content || content.trim() === '') {
          return ''
        }

        // 处理常见的markdown语法
        let processedContent = content
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // 粗体
          .replace(/\*(.*?)\*/g, '<em>$1</em>') // 斜体
          .replace(/`(.*?)`/g, '<code>$1</code>') // 行内代码
          .replace(/\n\n/g, '</p><p>') // 段落
          .replace(/\n/g, '<br>') // 换行

        // 处理列表
        processedContent = processedContent.replace(/^\- (.*?)$/gm, '<li>$1</li>')
        processedContent = processedContent.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')

        // 处理标题
        processedContent = processedContent.replace(/^### (.*?)$/gm, '<h3>$1</h3>')
        processedContent = processedContent.replace(/^## (.*?)$/gm, '<h2>$1</h2>')
        processedContent = processedContent.replace(/^# (.*?)$/gm, '<h1>$1</h1>')

        // 包装段落
        if (!processedContent.includes('<p>') && !processedContent.includes('<h') && !processedContent.includes('<ul>')) {
          processedContent = '<p>' + processedContent + '</p>'
        }

        return processedContent
      } catch (error) {
        console.error('Markdown渲染失败:', error)
        return content.replace(/\n/g, '<br>')
      }
    },
    
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    
    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },
    
    generateConversationId() {
      return 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    }
  }
}
</script>

<style lang="less" scoped>
.chatbot-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .common-questions {
    flex: 1;
    padding: 20px;
    
    .questions-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #333;
    }
    
    .questions-list {
      .question-item {
        padding: 12px 16px;
        margin-bottom: 8px;
        background: #f8f9fa;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        
        .anticon {
          margin-right: 8px;
          color: #1890ff;
        }
        
        &:hover {
          background: #e6f7ff;
          transform: translateY(-1px);
        }
      }
    }
  }
  
  .chat-messages {
    flex: 1;
    padding: 20px;
    
    .message-item {
      display: flex;
      margin-bottom: 16px;
      
      &.user {
        flex-direction: row-reverse;
        
        .message-content {
          margin-right: 12px;
          margin-left: 0;
          
          .message-text {
            background: #1890ff;
            color: #fff;
          }
        }
      }
      
      &.assistant {
        .message-content {
          margin-left: 12px;
          
          .message-text {
            background: #f5f5f5;
            color: #333;
          }
        }
      }
      
      .message-avatar {
        flex-shrink: 0;
      }
      
      .message-content {
        max-width: 70%;
        
        .message-text {
          padding: 12px 16px;
          border-radius: 12px;
          word-wrap: break-word;
          line-height: 1.5;
          
          /deep/ h1, /deep/ h2, /deep/ h3, /deep/ h4, /deep/ h5, /deep/ h6 {
            margin: 8px 0 4px 0;
            font-weight: 600;
          }
          
          /deep/ p {
            margin: 4px 0;
          }
          
          /deep/ ul, /deep/ ol {
            margin: 8px 0;
            padding-left: 20px;
          }
          
          /deep/ code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
          }
          
          /deep/ pre {
            background: rgba(0, 0, 0, 0.05);
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 8px 0;
          }
        }
        
        .message-time {
          font-size: 12px;
          color: #999;
          margin-top: 4px;
          text-align: right;
        }
      }
    }
    
    .typing-indicator {
      padding: 12px 16px;
      background: #f5f5f5;
      border-radius: 12px;
      display: flex;
      align-items: center;
      
      span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #999;
        margin-right: 4px;
        animation: typing 1.4s infinite ease-in-out;
        
        &:nth-child(1) { animation-delay: -0.32s; }
        &:nth-child(2) { animation-delay: -0.16s; }
        &:nth-child(3) { margin-right: 0; }
      }
    }
  }
  
  .chat-input {
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;
    
    .input-wrapper {
      display: flex;
      gap: 8px;
      
      .message-input {
        flex: 1;
      }
      
      .send-button {
        flex-shrink: 0;
      }
    }
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}
</style>
