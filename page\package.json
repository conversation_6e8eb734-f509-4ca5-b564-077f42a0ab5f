{"name": "jiumiao", "version": "7.5.21", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "build": "vue-cli-service build", "build:prod": "vue-cli-service build --mode prod", "build:test": "vue-cli-service build --mode test", "build:dev": "vue-cli-service build --mode dev", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@antv/data-set": "^0.10.1", "@fullcalendar/core": "^5.8.0", "@fullcalendar/daygrid": "^5.8.0", "@fullcalendar/interaction": "^5.8.0", "@fullcalendar/list": "^5.8.0", "@fullcalendar/resource-timegrid": "^5.8.0", "@fullcalendar/timegrid": "^5.8.0", "@fullcalendar/vue": "^5.8.0", "@tinymce/tinymce-vue": "^3.2.8", "@vue/composition-api": "^1.7.2", "ant-design-vue": "^1.7.6", "apexcharts": "^3.6.5", "axios": "^0.18.0", "clipboard": "^2.0.4", "codemirror": "^5.46.0", "dayjs": "^1.8.0", "enquire.js": "^2.1.6", "js-cookie": "^2.2.0", "lodash.get": "^4.4.2", "lodash.pick": "^4.4.0", "marked": "^4.3.0", "md5": "^2.2.1", "nprogress": "^0.2.0", "qrcode.vue": "^1.7.0", "sass": "^1.63.6", "tinymce": "^5.10.7", "tlbs-map-vue": "^1.3.1", "viser-vue": "^2.4.4", "vue": "2.6.11", "vue-apexcharts": "^1.3.2", "vue-class-component": "^6.0.0", "vue-clipboard2": "^0.3.1", "vue-cropper": "^0.4.8", "vue-i18n": "^8.7.0", "vue-loader": "^15.7.0", "vue-ls": "^3.2.0", "vue-markdown-it": "^0.9.4", "vue-photo-preview": "^1.1.3", "vue-print-nb-jeecg": "^1.0.7", "vue-property-decorator": "^7.3.0", "vue-router": "^3.0.1", "vue-splitpane": "^1.0.4", "vue-ueditor-wrap": "2.x", "vuedraggable": "^2.20.0", "vuex": "^3.0.1", "vuex-class": "^0.3.1", "vxe-table": "3.9.21", "xe-utils": "3.5.7", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/polyfill": "^7.4.4", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.3.0", "@vue/cli-service": "^3.3.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.1", "eslint": "^5.12.0", "eslint-plugin-vue": "^5.1.0", "less": "^3.8.1", "less-loader": "^4.1.0", "sass-loader": "^7.0.1", "uglifyjs-webpack-plugin": "^2.2.0", "vue-template-compiler": "2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/strongly-recommended", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"generator-star-spacing": "off", "no-mixed-operators": 0, "vue/max-attributes-per-line": "off", "vue/attribute-hyphenation": 0, "vue/html-self-closing": 0, "vue/component-name-in-template-casing": 0, "vue/html-closing-bracket-spacing": 0, "vue/singleline-html-element-content-newline": 0, "vue/no-unused-components": 0, "vue/multiline-html-element-content-newline": 0, "vue/no-use-v-if-with-v-for": 0, "vue/html-closing-bracket-newline": 0, "vue/no-parsing-error": 0, "no-console": 0, "vue/html-indent": "off", "vue/html-quotes": "off"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}