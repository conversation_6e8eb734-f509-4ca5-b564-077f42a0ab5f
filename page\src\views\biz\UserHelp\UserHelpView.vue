<template>
  <div class="help-view-container">
    <!-- 主要内容区域：左右布局 -->
    <div class="main-content">
      <!-- 左侧：智能客服聊天区域 -->
      <div class="chat-section">
        <div class="chat-header">
          <h3>
            <a-icon type="message" />
            智能客服
          </h3>
          <p>有问题随时问我，我会尽力帮助您！</p>
        </div>
        <div class="chat-wrapper">
          <chat-bot />
        </div>
      </div>

      <!-- 右侧：视频列表区域 -->
      <div class="video-section">
        <div class="video-header">
          <h3>
            <a-icon type="video-camera" />
            视频教程
          </h3>
        </div>

        <a-spin :spinning="loading">
          <div v-if="helpList.length > 0">
            <a-row :gutter="[16, 16]">
              <a-col :xs="24" :sm="12" :md="8" v-for="(item, index) in helpList" :key="item.id">
                <a-card class="help-card" size="small">
                  <div class="card-cover" @click="handleVideoClick(item)">
                    <img
                      v-if="item.coverUrl"
                      :src="item.coverUrl"
                      class="card-cover-img"
                      :alt="item.helpName"
                    />
                    <div v-else class="default-cover">
                      <a-icon type="video-camera" />
                    </div>
                    <div class="video-play-overlay">
                      <div class="play-button">
                        <a-icon type="play-circle" theme="filled" />
                      </div>
                    </div>
                  </div>
                  <a-card-meta :title="item.helpName" class="card-meta">
                    <template slot="description">
                      <div class="card-desc">
                        <p>{{ item.description || '暂无描述' }}</p>
                      </div>
                    </template>
                  </a-card-meta>

                  <!-- 关联文档直接显示在卡片下方 -->
                  <div class="card-docs" v-if="getRelatedDocs(item).length > 0">
                    <div class="docs-container">
                      <span class="docs-header">相关文档：</span>
                      <div class="docs-list">
                        <a-tooltip v-for="doc in getRelatedDocs(item)" :key="doc.id" :title="doc.description">
                          <a-tag class="doc-tag" @click.stop="openRelatedDoc(doc.url)">
                            <a-icon type="file-text" /> {{ doc.name }}
                          </a-tag>
                        </a-tooltip>
                      </div>
                    </div>
                  </div>
                </a-card>
              </a-col>
            </a-row>

            <!-- 分页 -->
            <div class="pagination-container">
              <a-pagination
                :current="ipagination.current"
                :pageSize="ipagination.pageSize"
                :total="ipagination.total"
                :showTotal="(total) => `共 ${total} 条`"
                :pageSizeOptions="['12', '24', '36', '48']"
                showSizeChanger
                @change="handleChange"
                @showSizeChange="handleShowSizeChange"
              />
            </div>
          </div>
          <a-empty v-else description="暂无帮助视频" />
        </a-spin>
      </div>
    </div>
    
    <!-- 视频播放弹窗 -->
    <a-modal
      :title="currentHelp.helpName"
      :visible="videoVisible"
      :footer="null"
      @cancel="closeVideo"
      :width="modalWidth"
      :destroyOnClose="true"
      centered
      class="video-modal"
    >
      <div class="video-container">
        <div class="video-wrapper">
          <video
            v-if="currentHelp.videoUrl"
            :src="currentHelp.videoUrl"
            controls
            autoplay
            class="video-player"
            ref="videoPlayer"
          ></video>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { getAction } from '@/api/manage'
import ChatBot from '@/components/ChatBot/ChatBot.vue'

export default {
  name: 'UserHelpView',
  components: {
    ChatBot
  },
  data() {
    return {
      description: '帮助中心',
      // 查询条件
      queryParam: {},
      // 数据集
      helpList: [],
      // 分页参数
      ipagination: {
        current: 1,
        pageSize: 12,
        total: 0
      },
      // 加载状态
      loading: false,
      // 视频播放
      videoVisible: false,
      currentHelp: {},
      // 搜索和筛选
      searchText: '',
      selectedUserType: '',
      modalWidth: '80%'
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1
      }
      
      const params = {
        pageNo: this.ipagination.current,
        pageSize: this.ipagination.pageSize,
        ...this.queryParam
      }
      
      // 添加筛选条件
      if (this.searchText) {
        params.helpName = this.searchText
      }
      
      if (this.selectedUserType) {
        params.targetUser = this.selectedUserType
      }
      
      this.loading = true
      getAction('/biz/userHelp/list', params).then(res => {
        if (res.success) {
          this.helpList = res.result.records || []
          this.ipagination.total = res.result.total || 0
        }
        this.loading = false
      }).catch(err => {
        this.loading = false
        this.$message.error('获取数据失败')
        console.error(err)
      })
    },
    // 分页
    handleChange(page) {
      this.ipagination.current = page
      this.loadData()
    },
    handleShowSizeChange(current, size) {
      this.ipagination.current = current
      this.ipagination.pageSize = size
      this.loadData()
    },
    // 搜索
    onSearch(value) {
      this.searchText = value
      this.loadData(1)
    },
    // 用户类型筛选
    onUserTypeChange(value) {
      this.loadData(1)
    },
    // 获取关联文档
    getRelatedDocs(item) {
      if (!item.relatedDocs) {
        return []
      }
      
      try {
        const docs = JSON.parse(item.relatedDocs)
        return docs.sort((a, b) => a.order - b.order)
      } catch (e) {
        console.error('解析关联文档失败', e)
        return []
      }
    },
    // 点击视频播放
    handleVideoClick(item) {
      this.currentHelp = item
      this.videoVisible = true
    },
    // 关闭视频
    closeVideo() {
      this.videoVisible = false
      this.currentHelp = {}
    },
    // 打开关联文档
    openRelatedDoc(url) {
      if (url) {
        window.open(url, '_blank')
      } else {
        this.$message.warning('链接无效')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.help-view-container {
  padding: 24px;
  background: #fff;
  min-height: 100vh;

  .page-header {
    text-align: center;
    margin-bottom: 24px;

    h2 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }
  }

  .main-content {
    display: flex;
    gap: 24px;
    min-height: calc(100vh - 120px);

    .chat-section {
      width: 400px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;

      .chat-header {
        margin-bottom: 16px;

        h3 {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #333;

          .anticon {
            margin-right: 8px;
            color: #1890ff;
          }
        }

        p {
          color: #666;
          margin: 0;
          font-size: 14px;
        }
      }

      .chat-wrapper {
        height: 700px;
        min-height: 0;

        /deep/ .chatbot-container {
          height: 100%;
        }
      }
    }

    .video-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;

      .video-header {
        margin-bottom: 16px;

        h3 {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #333;

          .anticon {
            margin-right: 8px;
            color: #1890ff;
          }
        }
      }
    }
  }

  .help-card {
    height: 100%;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .card-cover {
      padding-top: 56.25%; /* 16:9 比例 */
      position: relative;
      overflow: hidden;
      background: #f5f5f5;
      cursor: pointer;

      &:hover {
        .video-play-overlay {
          opacity: 1;
        }
      }
    }
    
    .card-cover-img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .default-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .anticon {
        font-size: 48px;
        color: #bfbfbf;
      }
    }
    
    .video-play-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 1;
      transition: all 0.3s;
      
      .play-button {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s;
        
        &:hover {
          transform: scale(1.1);
          background: rgba(0, 0, 0, 0.7);
        }
        
        .anticon {
          font-size: 36px;
          color: #fff;
        }
      }
      
      &:hover {
        background: rgba(0, 0, 0, 0.4);
      }
    }
    
    .card-desc {
      height: 32px;
      overflow: hidden;

      p {
        margin-bottom: 4px;
        height: 32px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        font-size: 12px;
        line-height: 1.4;
      }
    }
    
    .card-docs {
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px dashed #e8e8e8;
      
      .docs-container {
        display: flex;
        align-items: center;
      }
      
      .docs-header {
        font-size: 13px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        margin-right: 8px;
        white-space: nowrap;
        line-height: 22px;
      }
      
      .docs-list {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
        
        .doc-tag {
          cursor: pointer;
          margin: 0 8px 8px 0;
          padding: 4px 10px;
          font-size: 13px;
          background: #f0f7ff;
          border-color: #d0e8ff;
          color: #1890ff;
          line-height: 1.5;
          
          &:hover {
            background: #e6f3ff;
            border-color: #1890ff;
          }
        }
      }
    }
    
    .card-meta {
      margin-top: 14px;
    }

    /deep/ .ant-card-meta-title {
      font-size: 16px;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  
  .pagination-container {
    text-align: center;
    margin-top: 24px;
  }
  
  .video-modal {
    /deep/ .ant-modal-body {
      padding: 0;
      overflow: hidden;
    }
    .video-container {
      width: 100%;
      background: #000;
      border-radius: 4px;
      overflow: hidden;
      
      .video-wrapper {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 比例 */
      }
      
      .video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .help-view-container {
    .main-content {
      flex-direction: column;
      min-height: auto;

      .chat-section {
        width: 100%;
        margin-bottom: 24px;

        .chat-wrapper {
          height: 500px;

          /deep/ .chatbot-container {
            height: 100%;
          }

          /deep/ .chat-messages {
            max-height: 380px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .help-view-container {
    padding: 16px;

    .main-content {
      gap: 16px;

      .video-section {
        .ant-col {
          margin-bottom: 16px;
        }
      }
    }
  }
}
</style> 